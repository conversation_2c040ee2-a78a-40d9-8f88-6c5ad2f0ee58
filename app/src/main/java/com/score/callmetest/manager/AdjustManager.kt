package com.score.callmetest.manager

import android.app.Activity
import android.app.Application
import android.app.Application.ActivityLifecycleCallbacks
import android.content.Context
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustAttribution
import com.adjust.sdk.AdjustConfig
import com.adjust.sdk.AdjustEvent
import com.adjust.sdk.LogLevel
import com.adjust.sdk.OnAttributionChangedListener
import com.score.callmetest.BuildConfig
import com.score.callmetest.network.AfAttributionRecordRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.HeaderUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.net.URLEncoder

/**
 * 事件识别码在 README.md 中
 */
object AdjustManager {
    var attributionSdkVer: String = "5.4.1"

    private const val APP_TOKEN = "3m37ne0t4mps"
    private const val CREATE_ORDER = "av7eda"
    private const val LOGIN = "um5wb3"
    private const val REGISTER = "2jp2y3"
    private const val PAY_SUCCESS = "rykn2x"

    fun init(application: Application, appToken: String = APP_TOKEN) {
        val config = AdjustConfig(
            application,
            appToken,
            if (BuildConfig.DEBUG) AdjustConfig.ENVIRONMENT_SANDBOX else AdjustConfig.ENVIRONMENT_PRODUCTION
        ).apply {
            setLogLevel(if (BuildConfig.DEBUG) LogLevel.VERBOSE else LogLevel.WARN)
            onAttributionChangedListener = OnAttributionChangedListener { attribution ->
                // 更新HeaderUtils中的归因数据
                HeaderUtils.adjustAttribution = attribution
                reportAdjustAttribution(application, attribution, UserInfoManager.myUserInfo?.userId)
            }
        }
        Adjust.initSdk(config)
        application.registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityResumed(activity: Activity) { Adjust.onResume() }
            override fun onActivityPaused(activity: Activity) { Adjust.onPause() }
            override fun onActivityCreated(activity: Activity, savedInstanceState: android.os.Bundle?) {}
            override fun onActivityStarted(activity: Activity) {}
            override fun onActivityStopped(activity: Activity) {}
            override fun onActivitySaveInstanceState(activity: Activity, outState: android.os.Bundle) {}
            override fun onActivityDestroyed(activity: Activity) {}
        })
        Adjust.getSdkVersion { ver ->
            attributionSdkVer = ver
        }
    }

    fun reportAdjustAttribution(context: Context, attribution: AdjustAttribution, userId: String? = null) {
        val encode = { s: String? -> URLEncoder.encode(s ?: "", "UTF-8") }
        val request = AfAttributionRecordRequest(
            attributionSdk = "AJ",
            attributionSdkVer = attributionSdkVer,
            utmSource = encode(attribution.network),
            adgroupId = encode(attribution.adgroup),
            adsetId = encode(attribution.creative),
            campaignId = encode(attribution.campaign),
            fbInstallReferrer = encode(attribution.fbInstallReferrer),
            adset = "",
            afChannel = "",
            afStatus = "",
            agency = "",
            campaign = encode(attribution.campaign),
            createTime = null, // Adjust没有提供
            deviceId = DeviceUtils.getAndroidId(),
            id = null, // 没有对应字段
            pkg = context.packageName,
            userId = userId ?: "",
            ver = com.score.callmetest.BuildConfig.VERSION_NAME
        )
        CoroutineScope(Dispatchers.IO).launch {
            try {
                RetrofitUtils.dataRepository.reportAfAttribution(request)
            } catch (e: Exception) {
                // 日志上报失败
            }
        }
    }

    /**
     * 登录后主动上报一次归因数据，需传userId
     */
    fun reportLastAdjustAttribution(context: Context, userId: String?) {
        val attr = HeaderUtils.adjustAttribution ?: return
        reportAdjustAttribution(context, attr, userId)
    }

    /**
     * 上报创建订单事件
     */
    fun reportOrderEvent(orderId: String, revenue: Double, currency: String = "USD") {
        val event = AdjustEvent(CREATE_ORDER)
        event.deduplicationId = orderId
        event.setRevenue(revenue, currency)
        Adjust.trackEvent(event)
    }

    /**
     * 上报购买成功事件
     */
    fun reportPaySuccessEvent(orderId: String, revenue: Double, currency: String = "USD") {
        val event = AdjustEvent(PAY_SUCCESS)
        event.deduplicationId = orderId
        event.setRevenue(revenue, currency)
        Adjust.trackEvent(event)
    }

    /**
     * 判断当前应用是否来自广告来源
     * 通过Adjust归因数据判断，如果有network、campaign、adgroup等信息则认为是广告来源
     *
     * @return true: 广告来源, false: 非广告来源
     */
    fun isFromAdvertisementSource(): Boolean {
        val attribution = HeaderUtils.adjustAttribution
        return attribution?.let { attr ->
            // 如果有network信息且不为空，认为是广告来源
            !attr.network.isNullOrEmpty() ||
                    // 或者有campaign信息且不为空，认为是广告来源
                    !attr.campaign.isNullOrEmpty() ||
                    // 或者有adgroup信息且不为空，认为是广告来源
                    !attr.adgroup.isNullOrEmpty()
        } ?: false // 如果没有归因数据，认为不是广告来源
    }

} 