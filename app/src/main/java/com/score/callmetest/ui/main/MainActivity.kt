package com.score.callmetest.ui.main

import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.opensource.svgaplayer.SVGAImageView
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.databinding.ActivityMainBinding
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.manager.InstallReferrerManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.home.HomeFragment
import com.score.callmetest.ui.message.MessageFragment
import com.score.callmetest.ui.message.MessageIncomingManager
import com.score.callmetest.ui.mine.MineFragment
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber


class MainActivity : BaseActivity<ActivityMainBinding, MainViewModel>() {

    private val tabItems = listOf(
        TabItem(R.drawable.fire2, HomeFragment::class.java),
        TabItem(R.drawable.msg, MessageFragment::class.java),
        TabItem(R.drawable.mine, MineFragment::class.java)
    )

    private val tabSvgaMap = mapOf(
        0 to "fire.svga",
        1 to "msg.svga",
        2 to "mine.svga"
    )

    private val fragments: MutableList<Fragment?>
        get() = fieldBackingFragments.also {
            if (it.size != tabItems.size) {
                if (it.size < tabItems.size) {
                    repeat(tabItems.size - it.size) { it2 -> it.add(null) }
                } else {
                    while (it.size > tabItems.size) it.removeAt(it.size - 1)
                }
            }
        }
    private var fieldBackingFragments = MutableList<Fragment?>(tabItems.size) { null }
    private var currentTabIndex = -1


    override fun getViewBinding(): ActivityMainBinding {
        return ActivityMainBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = MainViewModel::class.java

    override fun initView() {
        ThreadUtils.runOnIO {
            // todo-dsc 融云 --connect,后续connect的位置还细考量
            // 融云获取token
            viewModel.getRongToken(
                onSuccess = { token ->
                    // 融云R
                    RongCloudManager.connect(token?:"")
                    Timber.d("rcToken---$token")
                },
                onError = { e ->
                    Timber.e("getRcToken---$e")
                }
            )
        }

        setupCustomTabs()
        switchTab(0)
    }

    override fun initData() {
        viewModel.uploadRiskInfo()
        // 初始化时加载消息数量
        loadInitialMessageCount()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ThreadUtils.runOnIO {
            InstallReferrerManager.tryReport(this)
        }
    }

    private fun setupCustomTabs() {
        val tabLayout = binding.customBottomTab
        // 允许子视图超出边界显示（用于消息数量徽章）
        tabLayout.clipChildren = false
        tabLayout.clipToPadding = false
        tabLayout.removeAllViews()
        tabItems.forEachIndexed { index, tabItem ->
            val tabView = layoutInflater.inflate(R.layout.nav_tab_custom, tabLayout, false)
            val iconView = tabView.findViewById<ImageView>(R.id.tab_icon)
            val svgaView = tabView.findViewById<SVGAImageView>(R.id.tab_svga)
            val sumView = tabView.findViewById<TextView>(R.id.tab_sum)

            // 设置初始图标（未选中状态）
            iconView.setImageResource(tabItem.iconRes)
            iconView.visibility = View.VISIBLE
            svgaView.visibility = View.GONE

            // 只在消息tab（index==1）时初始化sum，具体显示由数据驱动
            if (index == 1) {
                sumView.visibility = View.GONE  // 初始隐藏，等待数据更新
                sumView.text = "0"
            } else {
                sumView.visibility = View.GONE
            }
            tabView.click {
                switchTab(index)
                // 可以显示消息弹窗
                MessageIncomingManager.setMsgListFragmentVisible(index == 1)
                if(index == 1 || index == 2) {
                    // 目前只需要通知当前点击了message的item，其他如果需要，再改动
                    EventBus.post(CustomEvents.BottomTabSelected(index))
                }

            }
            tabLayout.addView(
                tabView,
                LinearLayout.LayoutParams(
                    0,
                    LinearLayout.LayoutParams.MATCH_PARENT
                ).apply {
                    weight = 1.0f
                })
        }

        // 适配底部安全区域，防止custom_bottom_tab被系统导航栏遮挡
//        ViewCompat.setOnApplyWindowInsetsListener(tabLayout) { v, insets ->
//            val bottom = insets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
//            v.setPadding(v.paddingLeft, v.paddingTop, v.paddingRight, bottom)
//            insets
//        }
    }

    private fun switchTab(index: Int) {
        val tag = tabItems[index].clazz.simpleName
        val fragmentManager = supportFragmentManager
        val transaction = fragmentManager.beginTransaction()
        
        // 隐藏当前Fragment
        if (currentTabIndex != -1 && fragments[currentTabIndex] != null) {
            transaction.hide(fragments[currentTabIndex]!!)
        }
        
        // 显示或创建目标Fragment
        var fragment = fragments[index]
        if (fragment == null) {
            fragment = tabItems[index].clazz.newInstance()
            fragments[index] = fragment
            transaction.add(R.id.fragment_container, fragment, tag)
        } else {
            transaction.show(fragment)
        }
        
        transaction.commitAllowingStateLoss()
        currentTabIndex = index
        updateTabSelection(index)
    }

    private fun updateTabSelection(selectedIndex: Int) {
        val tabLayout = binding.root.findViewById<LinearLayout>(R.id.custom_bottom_tab)
        for (i in 0 until tabLayout.childCount) {
            val tabView = tabLayout.getChildAt(i)
            val iconView = tabView.findViewById<ImageView>(R.id.tab_icon)
            val svgaView = tabView.findViewById<SVGAImageView>(R.id.tab_svga)

            if (i == selectedIndex) {
                if ( selectedIndex == 1) {
                    svgaView.visibility = View.GONE
                    iconView.visibility = View.VISIBLE
                    iconView.setColorFilter("#FFFF8C00".toColorInt())
                } else {
                    iconView.visibility = View.GONE
                    svgaView.visibility = View.VISIBLE
                    CustomUtils.playSvgaOnce(svgaView, tabSvgaMap[i])
                }

            } else {
                iconView.visibility = View.VISIBLE
                svgaView.visibility = View.GONE
                svgaView.stopAnimation(true)
                // 清除颜色过滤器，恢复原始图标颜色
                iconView.clearColorFilter()
            }
        }
    }

    /**
     * 加载初始消息数量
     * 在应用启动时调用，确保消息数量能够正确显示
     */
    private fun loadInitialMessageCount() {
        lifecycleScope.launch {
            try {
                // 直接从数据库获取消息列表并计算未读数量
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .getCurrentUserAllMessageLists()
                    .catch { e ->
                        Timber.e("loadInitialMessageCount error: $e")
                    }
                    .collect { messageList ->
                        // 计算总未读数量
                        val totalUnreadCount = messageList.sumOf { it.unreadCount }
                        // 更新UI（需要在主线程执行）
                        updateMessageCount(totalUnreadCount)
                        Timber.d("Initial message count loaded: $totalUnreadCount")
                    }
            } catch (e: Exception) {
                Timber.e("Failed to load initial message count: $e")
            }
        }
    }

    /**
     * 更新消息tab的数量显示
     * @param count 消息数量，0表示隐藏，大于99显示99+
     */
    fun updateMessageCount(count: Int) {
        val tabLayout = binding.root.findViewById<LinearLayout>(R.id.custom_bottom_tab)
        if (tabLayout.childCount > 1) {
            val messageTabView = tabLayout.getChildAt(1) // 消息tab是index=1
            val sumView = messageTabView.findViewById<TextView>(R.id.tab_sum)

            // 确保sumView不为null
            sumView?.let { sum ->
                if (count > 0) {
                    sum.visibility = View.VISIBLE
                    sum.text = if (count > 99) "99+" else count.toString()
                } else {
                    sum.visibility = View.GONE
                }
            }
        }
    }

    fun getCurrentTabIndex(): Int {
        return currentTabIndex
    }

    data class TabItem(val iconRes: Int, val clazz: Class<out Fragment>)
    
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            val fm = supportFragmentManager
            // 如果有Fragment在back stack，先让FragmentManager处理
            if (fm.backStackEntryCount > 0) {
                fm.popBackStack()
            } else {
                backToDesktop()
            }
            backToDesktop()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    fun backToDesktop() {
        // 直接返回桌面，不退出应用
        val homeIntent = Intent(Intent.ACTION_MAIN)
        homeIntent.addCategory(Intent.CATEGORY_HOME)
        homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        startActivity(homeIntent)
    }
}